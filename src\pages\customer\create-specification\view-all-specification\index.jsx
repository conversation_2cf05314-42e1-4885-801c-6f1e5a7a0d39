import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { SpecTemplate1Modified, SpecTemplate2Modified, SpecTemplate3Modified } from "@/components/shared/specification-templates";
import { Button } from "@/components/ui/button";
import { Filter, X, Eye } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { useGetAllSpecificationsQuery } from "@/services/customer/query";

const templates = {
  1: SpecTemplate1Modified,
  2: SpecTemplate2Modified,
  3: SpecTemplate3Modified,
};

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    // Handle category/subcategory objects with name property
    if (value.name) return String(value.name);
    // Handle other objects by converting to JSON
    return JSON.stringify(value);
  }
  return String(value);
};

// Transform API response to match component expectations
const transformSpecificationsData = (apiData) => {
  console.log("Raw specifications API data:", apiData);

  if (!apiData) {
    console.log("No API data received for specifications");
    return [];
  }

  if (!Array.isArray(apiData)) {
    console.log("API data is not an array:", typeof apiData);
    return [];
  }

  const transformed = apiData
    .map((spec) => {
      if (!spec) return null;

      return {
        id: spec.id || null,
        title: spec.title || "",
        description: spec.description || "",
        type: spec.type || "",
        // Handle category - it might be an object or a string
        category:
          typeof spec.category === "object" && spec.category?.name
            ? spec.category.name
            : spec.category || "",
        // Handle sub_category - it might be an object or a string
        subCategory:
          typeof spec.sub_category === "object" && spec.sub_category?.name
            ? spec.sub_category.name
            : spec.sub_category || "",
        createdAt: spec.created_at || "",
        template_id: spec.template_id || 1,
        status: spec.status || "",
        items_count: spec.items_count || 0,
        // Transform items to match the expected specificationProductData format
        specificationProductData: Array.isArray(spec.items)
          ? spec.items.map((item) => ({
              itemName: item?.item_name || "",
              quantity: parseFloat(item?.quantity) || 0,
              unit: item?.unit || "",
              attributes: item?.specifications || "",
              other: item?.other || "",
            }))
          : [],
      };
    })
    .filter(Boolean); // Remove any null entries

  console.log("Transformed specifications data:", transformed);
  return transformed;
};

export default function ViewAllSpecifications() {
  const navigate = useNavigate();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "",
    subCategory: "",
    date: "",
  });

  // Use the API hook to fetch specifications data
  const {
    data: apiResponse,
    isLoading,
    error,
    isError,
  } = useGetAllSpecificationsQuery();

  // Transform the API response data
  const specs = transformSpecificationsData(apiResponse?.data);

  // Get unique categories and subcategories for dropdowns
  const categories = [
    ...new Set(specs.map((spec) => spec.category).filter(Boolean)),
  ];
  const subCategories = [
    ...new Set(specs.map((spec) => spec.subCategory).filter(Boolean)),
  ];

  const handleFilterChange = (name, value) => {
    setFilters({ ...filters, [name]: value });
  };

  const resetFilters = () => {
    setFilters({
      category: "",
      subCategory: "",
      date: "",
    });
  };

  const filteredSpecs = specs.filter((spec) => {
    return (
      (filters.category === "all" ||
        !filters.category ||
        spec.category === filters.category) &&
      (filters.subCategory === "all" ||
        !filters.subCategory ||
        spec.subCategory === filters.subCategory) &&
      (filters.date ? spec.createdAt === filters.date : true)
    );
  });

  const handleViewSpecification = (id) => {
    navigate(`/customer/1/specification-view/${id}`);
  };

  if (isLoading) {
    return <div className="p-6 text-center">Loading specifications...</div>;
  }

  if (isError) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4 text-red-600">
          Error loading specifications
        </h2>
        <p className="text-gray-600 mb-4">
          {error?.message || "Something went wrong"}
        </p>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Specifications</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <Label className="mb-2 block">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) =>
                    handleFilterChange("category", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Sub Category</Label>
                <Select
                  value={filters.subCategory}
                  onValueChange={(value) =>
                    handleFilterChange("subCategory", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select sub-category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sub-categories</SelectItem>
                    {subCategories.map((subCategory) => (
                      <SelectItem key={subCategory} value={subCategory}>
                        {subCategory}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Date</Label>
                <Input
                  type="date"
                  value={filters.date}
                  onChange={(e) => handleFilterChange("date", e.target.value)}
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-white rounded-md border shadow-sm w-full">
        <Table className="cursor-pointer w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[20%]">Title</TableHead>
              <TableHead className="w-[15%]">Type</TableHead>
              <TableHead className="w-[15%]">Status</TableHead>
              <TableHead className="w-[15%]">Category</TableHead>
              <TableHead className="w-[15%]">Date</TableHead>
              <TableHead className="w-[10%]">Items</TableHead>
              <TableHead className="w-[10%] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSpecs.length > 0 ? (
              filteredSpecs.map((spec) => (
                <TableRow key={spec.id} className="hover:bg-gray-100">
                  <TableCell className="font-medium">
                    {safeRender(spec.title, `Specification #${spec.id}`)}
                  </TableCell>
                  <TableCell className="capitalize">
                    {safeRender(spec.type)}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        spec.status === "published"
                          ? "bg-green-100 text-green-800"
                          : spec.status === "draft"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {safeRender(spec.status)}
                    </span>
                  </TableCell>
                  <TableCell>{safeRender(spec.category)}</TableCell>
                  <TableCell>
                    {spec.createdAt
                      ? (() => {
                          try {
                            return format(
                              new Date(spec.createdAt),
                              "yyyy-MM-dd"
                            );
                          } catch (e) {
                            return spec.createdAt;
                          }
                        })()
                      : "N/A"}
                  </TableCell>
                  <TableCell>{safeRender(spec.items_count, "0")}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewSpecification(spec.id)}
                      className="flex items-center gap-1 ml-auto"
                    >
                      <Eye className="h-4 w-4" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={7}
                  className="text-center py-6 text-gray-500"
                >
                  No specifications found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
