import React from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Printer, Download } from "lucide-react";
import { SpecTemplate1Modified, SpecTemplate2Modified, SpecTemplate3Modified } from "@/components/shared/specification-templates";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useGetSpecificationById } from "@/services/vendor/query";

const templates = {
  1: SpecTemplate1Modified,
  2: SpecTemplate2Modified,
  3: SpecTemplate3Modified,
};

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    // Handle category/subcategory objects with name property
    if (value.name) return String(value.name);
    // Handle other objects by converting to JSON
    return JSON.stringify(value);
  }
  return String(value);
};

// Transform API response to match template expectations
const transformSpecificationData = (apiData) => {
  if (!apiData) return null;

  return {
    id: apiData.id,
    title: apiData.title,
    description: apiData.description,
    template_id: apiData.template_id,
    category: safeRender(apiData.category),
    subCategory: safeRender(apiData.subcategory),
    specificationType: safeRender(apiData.specification_type),
    specificationProductData: apiData.items?.map(item => ({
      product_name: safeRender(item.item_name),
      quantity: safeRender(item.quantity),
      unit: safeRender(item.unit),
      specifications: item.specifications,
      other: safeRender(item.other),
      itemName: safeRender(item.item_name), // For template compatibility
      attributes: item.specifications, // For template compatibility
    })) || [],
    created_at: apiData.created_at,
    updated_at: apiData.updated_at,
  };
};

export default function VendorSpecificationView() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Use the API hook to fetch specification data
  const {
    data: apiResponse,
    isLoading: loading,
    error,
    isError,
  } = useGetSpecificationById(id);

  console.log("Full API Response:", apiResponse);
  console.log("API Response Data:", apiResponse?.data);
  console.log("Loading:", loading);
  console.log("Error:", error);
  console.log("Is Error:", isError);

  // Transform the API response data
  const specification = transformSpecificationData(apiResponse?.data);

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Create a new window for printing/PDF generation
    const printWindow = window.open('', '_blank');
    const templateComponent = templates[specification?.template_id];
    
    if (templateComponent && specification) {
      // This is a simplified approach - in a real app you might want to use a proper PDF library
      printWindow.document.write(`
        <html>
          <head>
            <title>Specification - ${specification.title}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .print-only { display: block; }
              @media print { .no-print { display: none; } }
            </style>
          </head>
          <body>
            <div id="specification-content">
              <!-- Template content would be rendered here -->
              <h1>${specification.title}</h1>
              <p>Specification details...</p>
            </div>
            <script>
              window.onload = function() {
                window.print();
                window.close();
              }
            </script>
          </body>
        </html>
      `);
    }
  };

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <header className="flex h-12 shrink-0 items-center gap-2 mb-6">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb className="font-nunito">
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/vendor/1/feeds">
                    Feeds
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbLink href="#">
                    Specification Details
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading specification...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isError || error) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <header className="flex h-12 shrink-0 items-center gap-2 mb-6">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb className="font-nunito">
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/vendor/1/feeds">
                    Feeds
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbLink href="#">
                    Specification Details
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-500">
            <p className="text-lg font-semibold mb-2">Error Loading Specification</p>
            <p className="text-sm">{error?.message || "Failed to load specification data"}</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => navigate("/vendor/1/feeds")}
            >
              Back to Feeds
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const TemplateComponent = templates[specification?.template_id];

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <header className="flex h-12 shrink-0 items-center gap-2 mb-6">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/vendor/1/feeds">
                  Feeds
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem>
                <BreadcrumbLink href="#">
                  Specification Details
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/vendor/1/feeds")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Feeds
          </Button>
          <div>
            <h1 className="text-2xl font-semibold">
              {safeRender(
                specification?.title,
                `Specification #${specification?.id || id}`
              )}
            </h1>
            {specification?.description && (
              <p className="text-gray-600 mt-1">
                {safeRender(specification.description)}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 no-print">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download
          </Button>
        </div>
      </div>

      <div className="print:mt-8">
        {TemplateComponent && specification ? (
          <>
            {console.log("Passing data to template:", specification)}
            <TemplateComponent data={specification} />
          </>
        ) : (
          <div className="text-center text-red-500">
            {!specification
              ? "Specification data not available"
              : `Template not found for ID: ${
                  specification?.template_id || "unknown"
                }`}
          </div>
        )}
      </div>
    </div>
  );
}
