import React from "react";
import Logo from "@/assets/png/logo.png";
import { fixedFields } from "@/utils/specProductFields";
import { useGetCustomerProfileQuery } from "@/services/auth/query";

const SpecTemplate1Modified = ({ data }) => {
  const { specificationProductData, category, subCategory, specificationType } = data;
  const customerData = useGetCustomerProfileQuery();
  const { name, phone, email, address } = customerData?.data?.data;
  console.log('Template data:', data);
  console.log('Customer data:', customerData?.data?.data);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      {/* Header */}
      <div className="bg-green-300 p-4 flex justify-between items-start">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{name}</h1>
          <p className="text-base">{address || "Kathmandu, Nepal"}</p>
          <p className="text-base">{email}</p>
          <p className="text-base">{phone || "+977-9812343435"}</p>
        </div>
        <div>
          <img src={Logo} alt="logo" className="w-[150px] h-auto" />
        </div>
      </div>

      {/* Specification Details */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Specification Details</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span> {specificationType || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Category:</span> {category || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Sub-Category:</span> {subCategory || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Date:</span> {new Date().toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Specification Table */}
      {specificationProductData?.length > 0 && (
        <table className="w-full mt-4 border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">S.N.</th>
              <th className="border border-gray-300 p-2 text-left">Product Name</th>
              <th className="border border-gray-300 p-2 text-left">Quantity</th>
              <th className="border border-gray-300 p-2 text-left">Unit</th>
              <th className="border border-gray-300 p-2 text-left">Specifications</th>
              <th className="border border-gray-300 p-2 text-left">Other</th>
            </tr>
          </thead>
          <tbody>
            {specificationProductData.map((item, index) => (
              <tr key={index}>
                <td className="border border-gray-300 p-2">{index + 1}</td>
                <td className="border border-gray-300 p-2">{item.product_name || 'N/A'}</td>
                <td className="border border-gray-300 p-2">{item.quantity || 'N/A'}</td>
                <td className="border border-gray-300 p-2">{item.unit || 'N/A'}</td>
                <td className="border border-gray-300 p-2">
                  {item.specifications && typeof item.specifications === 'object' 
                    ? Object.entries(item.specifications).map(([key, value]) => (
                        <div key={key} className="mb-1">
                          <span className="font-medium">{fixedFields[key] || key}:</span> {value}
                        </div>
                      ))
                    : item.specifications || 'N/A'
                  }
                </td>
                <td className="border border-gray-300 p-2">{item.other || 'N/A'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {/* Footer */}
      <div className="mt-6 text-center text-sm text-gray-600">
        <p>This specification document was generated on {new Date().toLocaleDateString()}</p>
      </div>
    </div>
  );
};

export default SpecTemplate1Modified;
