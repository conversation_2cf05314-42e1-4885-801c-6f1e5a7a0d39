import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LuEllipsisVertical, LuDot, LuCalendar, Lu<PERSON>ser, LuTag } from "react-icons/lu";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";

// Utility function to safely render values
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    if (value.name) return String(value.name);
    return JSON.stringify(value);
  }
  return String(value);
};

const SpecificationCard = ({ specification }) => {
  const navigate = useNavigate();

  const handleViewDetails = () => {
    navigate(`/vendor/1/specification/${specification.id}`);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "published": return "bg-green-100 text-green-800";
      case "quoted": return "bg-blue-100 text-blue-800";
      case "closed": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="border border-darkMedium py-2 md:py-4 rounded-xl shadow-sm bg-white">
      {/* Header */}
      <div className="flex justify-between items-center px-2 md:px-4 border-b border-darkMedium pb-3">
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarImage src={specification.customer_avatar} alt={specification.customer_name} />
            <AvatarFallback>
              {specification.customer_name?.charAt(0) || "C"}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col pt-2">
            <span className="text-bodySmall font-medium font-nunito leading-3">
              {safeRender(specification.customer_name, "Customer")}
            </span>
            <span className="text-textSmall font-nunito text-gray-500">
              Customer
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(specification.status)}>
            {safeRender(specification.status, "Published")}
          </Badge>
          <Button className="bg-transparent px-2 transition-all duration-200">
            <LuEllipsisVertical className="w-4 h-4 text-navy" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="my-4 px-2 md:px-4">
        <h3 className="text-lg font-semibold mb-2">
          {safeRender(specification.title)}
        </h3>
        {specification.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {safeRender(specification.description)}
          </p>
        )}
        
        {/* Specification Details */}
        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
          <div className="flex items-center gap-1">
            <LuTag className="w-3 h-3" />
            <span className="font-medium">Category:</span>
            <span>{safeRender(specification.category)}</span>
          </div>
          <div className="flex items-center gap-1">
            <LuTag className="w-3 h-3" />
            <span className="font-medium">Sub-Category:</span>
            <span>{safeRender(specification.subCategory)}</span>
          </div>
          <div className="flex items-center gap-1">
            <LuCalendar className="w-3 h-3" />
            <span className="font-medium">Created:</span>
            <span>
              {specification.created_at 
                ? format(new Date(specification.created_at), "MMM dd, yyyy")
                : "N/A"
              }
            </span>
          </div>
          <div className="flex items-center gap-1">
            <LuUser className="w-3 h-3" />
            <span className="font-medium">Items:</span>
            <span>{specification.items_count || 0}</span>
          </div>
        </div>

        {/* Tags/Categories */}
        <div className="flex items-center font-nunito gap-1 mb-3">
          {[specification.category, specification.subCategory].filter(Boolean).map((tag, index) => (
            <div key={index} className="flex items-center gap-1">
              <span className="text-textSmall font-bold text-primary">{tag}</span>
              {index < 1 && specification.subCategory && <LuDot className="w-4 h-4" />}
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2 pt-2 border-t border-darkMedium px-2 md:px-4">
        <button className="flex items-center gap-2 w-fit h-[2rem] px-2 py-1 rounded-xl hover:bg-darkMedium">
          <LuThumbsUp className="w-4 h-4" />
          <span>Interested</span>
        </button>
        
        <Button
          onClick={handleViewDetails}
          variant="outline"
          size="sm"
          className="flex items-center gap-2 h-[2rem] px-3"
        >
          <LuEye className="w-4 h-4" />
          View Details
        </Button>
      </div>
    </div>
  );
};

export default SpecificationCard;
