import { useSuspenseQuery } from "@tanstack/react-query";
import { getSpecificationById, getSpecifications, getVendorBusinessCategory } from "./api";

export function useGetVendorBusinessCategory() {
    return useSuspenseQuery({
      queryKey: ["getVendorBusinessCategory"],
      queryFn: () => getVendorBusinessCategory(),
    });
  }

  export function useGetSpecifications() {
  return useSuspenseQuery({
    queryKey: ["getSpecifications"],
    queryFn: () => getSpecifications(),
  });
}

  export function useGetSpecificationById(id) {
    return useSuspenseQuery({
      queryKey: ["getSpecificationById", id],
      queryFn: () => getSpecificationById(id),
      enabled: !!id,
    });
  }