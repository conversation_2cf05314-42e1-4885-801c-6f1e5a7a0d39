import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Filter, X, Eye } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { useGetSpecifications } from "@/services/vendor/query";

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    // Handle category/subcategory objects with name property
    if (value.name) return String(value.name);
    // Handle other objects by converting to JSON
    return JSON.stringify(value);
  }
  return String(value);
};

// Transform API response to match component expectations
const transformSpecificationsData = (apiData) => {
  if (!apiData || !Array.isArray(apiData)) return [];

  return apiData.map((item) => ({
    id: item.id,
    title: safeRender(item.title),
    description: safeRender(item.description),
    category: safeRender(item.category),
    subCategory: safeRender(item.subcategory),
    specificationType: safeRender(item.specification_type),
    template_id: item.template_id,
    created_at: item.created_at,
    updated_at: item.updated_at,
    customer_name: safeRender(item.customer?.name || item.customer_name),
    items_count: item.items?.length || 0,
    status: item.status || 'published',
  }));
};

export default function VendorSpecificationList() {
  const navigate = useNavigate();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "",
    subCategory: "",
    date: "",
  });

  // Use the API hook to fetch specifications data
  const {
    data: apiResponse,
    isLoading,
    error,
    isError,
  } = useGetSpecifications();

  // Transform the API response data
  const specs = transformSpecificationsData(apiResponse?.data);

  // Get unique categories and subcategories for dropdowns
  const categories = [
    ...new Set(specs.map((spec) => spec.category).filter(Boolean)),
  ];
  const subCategories = [
    ...new Set(specs.map((spec) => spec.subCategory).filter(Boolean)),
  ];

  // Filter specifications based on current filters
  const filteredSpecs = specs.filter((spec) => {
    const matchesCategory = !filters.category || spec.category === filters.category;
    const matchesSubCategory = !filters.subCategory || spec.subCategory === filters.subCategory;
    const matchesDate = !filters.date || 
      format(new Date(spec.created_at), "yyyy-MM-dd") === filters.date;
    
    return matchesCategory && matchesSubCategory && matchesDate;
  });

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({ category: "", subCategory: "", date: "" });
  };

  const handleViewDetails = (specId) => {
    navigate(`/vendor/1/specification/${specId}`);
  };

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading specifications...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isError || error) {
    return (
      <div className="p-4">
        <div className="text-center text-red-500">
          <p className="text-lg font-semibold mb-2">Error Loading Specifications</p>
          <p className="text-sm">{error?.message || "Failed to load specifications"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Available Specifications</h1>
          <p className="text-gray-600">Browse customer specifications and view details</p>
        </div>
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          {showFilters ? "Hide Filters" : "Show Filters"}
        </Button>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={filters.category} onValueChange={(value) => handleFilterChange("category", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="subCategory">Sub-Category</Label>
                <Select value={filters.subCategory} onValueChange={(value) => handleFilterChange("subCategory", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Sub-Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Sub-Categories</SelectItem>
                    {subCategories.map((subCategory) => (
                      <SelectItem key={subCategory} value={subCategory}>
                        {subCategory}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  type="date"
                  value={filters.date}
                  onChange={(e) => handleFilterChange("date", e.target.value)}
                />
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={clearFilters} className="flex items-center gap-2">
                  <X className="h-4 w-4" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {filteredSpecs.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500">No specifications found matching your criteria.</p>
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Sub-Category</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Date Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSpecs.map((spec) => (
                  <TableRow key={spec.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{spec.title}</p>
                        {spec.description && (
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {spec.description}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{spec.customer_name}</TableCell>
                    <TableCell>{spec.category}</TableCell>
                    <TableCell>{spec.subCategory}</TableCell>
                    <TableCell>{spec.items_count}</TableCell>
                    <TableCell>
                      {spec.created_at ? format(new Date(spec.created_at), "MMM dd, yyyy") : "N/A"}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(spec.id)}
                        className="flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
