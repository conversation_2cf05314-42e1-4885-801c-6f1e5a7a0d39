import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import SpecificationCard from "./_components/SpecificationCard";
import VendorSpecificationList from "./_components/VendorSpecificationList";
import { useGetVendorProfileQuery } from "@/services/auth/query";
import { Dialog } from "@radix-ui/react-dialog";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useGetSpecifications } from "@/services/vendor/query";

// Utility function to safely render values
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    if (value.name) return String(value.name);
    return JSON.stringify(value);
  }
  return String(value);
};

// Transform API response to match component expectations
const transformSpecificationsData = (apiData) => {
  if (!apiData || !Array.isArray(apiData)) return [];

  return apiData.map((item) => ({
    id: item.id,
    title: safeRender(item.title),
    description: safeRender(item.description),
    category: safeRender(item.category),
    subCategory: safeRender(item.subcategory),
    specificationType: safeRender(item.specification_type),
    template_id: item.template_id,
    created_at: item.created_at,
    updated_at: item.updated_at,
    customer_name: safeRender(item.customer?.name || item.customer_name),
    customer_avatar: item.customer?.avatar,
    items_count: item.items?.length || 0,
    status: item.status || 'published',
  }));
};

const Feeds = () => {
  const { data: vendorData } = useGetVendorProfileQuery();
  const { data: specificationsResponse, isLoading: specificationsLoading } = useGetSpecifications();
  const [showKycDialog, setShowKycDialog] = useState(false);
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'

  console.log(vendorData?.vendor_details?.kyc_status);

  useEffect(() => {
    if (vendorData?.vendor_details?.kyc_status === "under_review") {
      setShowKycDialog(true);
    }
  }, [vendorData?.vendor_details?.kyc_status]);

  // Transform specifications data
  const specifications = transformSpecificationsData(specificationsResponse?.data);

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href={`#`}>Feeds</BreadcrumbLink>
              </BreadcrumbItem>
              {/* <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Data Fetching</BreadcrumbPage>
              </BreadcrumbItem> */}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="p-4 pt-0">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-semibold">Available Specifications</h2>
            <p className="text-gray-600">Browse customer specifications and submit quotes</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'cards' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('cards')}
            >
              Cards
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              Table
            </Button>
          </div>
        </div>

        {specificationsLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading specifications...</p>
            </div>
          </div>
        ) : viewMode === 'table' ? (
          <VendorSpecificationList />
        ) : (
          <div className="mt-6 grid gap-4">
            {specifications.length > 0 ? (
              specifications.map((specification) => (
                <SpecificationCard
                  key={specification.id}
                  specification={specification}
                />
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No specifications available at the moment.</p>
              </div>
            )}
          </div>
        )}
      </div>
      <Dialog open={showKycDialog} onOpenChange={setShowKycDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>KYC Verification Required</DialogTitle>
            <DialogDescription>
              Your KYC verification is currently pending. Please complete your
              KYC profile to access all features.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowKycDialog(false)}>
              Skip for Now
            </Button>
            <Button asChild>
              <Link to="/kyc-verify">Update KYC</Link>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Feeds;
